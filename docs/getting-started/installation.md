# Installation Guide

This guide covers the complete installation process for HiberNet Enhanced, including all dependencies and optional components.

> **Note**: This is the enhanced version of the original Hibernet project. See the [Credits](#credits) section for attribution to the original work.

## 📋 System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher
- **Operating System**: Linux, macOS, or Windows
- **Memory**: 512MB RAM minimum, 2GB recommended
- **Storage**: 100MB free space minimum

### Recommended Requirements
- **Python**: 3.9+ for optimal performance
- **Memory**: 4GB RAM for large-scale testing
- **Storage**: 1GB for logs and database
- **Network**: Stable internet connection for proxy scraping

## 🚀 Quick Installation

### 1. Clone the Repository
```bash
git clone https://github.com/skizap/HiberNet-Enhanced.git
cd HiberNet-Enhanced
```

### 2. Set Up Virtual Environment
```bash
# Create virtual environment
python3 -m venv hiber_venv

# Activate virtual environment
# Linux/macOS:
source hiber_venv/bin/activate
# Windows:
hiber_venv\Scripts\activate
```

### 3. Install Dependencies
```bash
# Install core dependencies
pip install -r requirements.txt

# Install optional dependencies for enhanced features
pip install h2 aioquic websockets geoip2
```

### 4. Verify Installation
```bash
# Test HibernetV3.0
python HibernetV3.0.py --help

# Test HiberProxy Enhanced
python -m hiber_proxy --help

# Test web interface
python start_web.py
```

## 📦 Detailed Installation

### Core Dependencies
```bash
# Essential packages
pip install requests pysocks flask flask-socketio flask-cors
pip install sqlite3 pyyaml colorama tabulate
```

### Protocol Support Dependencies
```bash
# HTTP/2 support
pip install h2

# HTTP/3 (QUIC) support  
pip install aioquic

# WebSocket support
pip install websockets

# GeoIP support for proxy geolocation
pip install geoip2
```

### Development Dependencies (Optional)
```bash
# For development and testing
pip install pytest pytest-cov black flake8 mypy
```

## 🔧 Configuration Setup

### 1. Create Configuration File
```bash
# Create default configuration
python -m hiber_proxy config --create-default config.json
```

### 2. Database Initialization
```bash
# Initialize SQLite database
python -m hiber_proxy init-db
```

### 3. Download GeoIP Database (Optional)
```bash
# Download GeoLite2 database for geolocation features
wget https://github.com/P3TERX/GeoLite.mmdb/raw/download/GeoLite2-City.mmdb
```

## 🌐 Web Interface Setup

### 1. Install Web Dependencies
```bash
# Web interface specific packages
pip install flask flask-socketio flask-cors
```

### 2. Start Web Server
```bash
# Start development server
python start_web.py

# Or use the web module directly
python -m hiber_proxy.web.run_server
```

### 3. Access Web Interface
Open your browser and navigate to:
- **Local**: http://localhost:5000
- **Network**: http://your-ip:5000

## 🐳 Docker Installation (Alternative)

### 1. Build Docker Image
```bash
# Build the container
docker build -t hibernet .
```

### 2. Run Container
```bash
# Run with port mapping
docker run -p 5000:5000 -p 8080:8080 hibernet

# Run with volume mounting for persistence
docker run -v $(pwd)/data:/app/data -p 5000:5000 hibernet
```

## 🔍 Verification & Testing

### 1. Test Core Functionality
```bash
# Test proxy management
python -m hiber_proxy add "http://127.0.0.1:8080"
python -m hiber_proxy list

# Test stress testing
python HibernetV3.0.py --target https://httpbin.org/get --threads 10 --duration 5
```

### 2. Test Web Interface
```bash
# Start web server
python start_web.py

# In another terminal, test API
curl http://localhost:5000/api/proxies
curl http://localhost:5000/api/stats
```

### 3. Run Test Suite (If Available)
```bash
# Run unit tests
python -m pytest tests/

# Run integration tests
python -m pytest tests/integration/
```

## 🛠️ Troubleshooting

### Common Issues

#### Python Version Issues
```bash
# Check Python version
python --version
python3 --version

# Use specific Python version
python3.9 -m venv hiber_venv
```

#### Permission Issues (Linux/macOS)
```bash
# Fix permissions
chmod +x HibernetV3.0.py
chmod +x start_web.py

# Install with user permissions
pip install --user -r requirements.txt
```

#### Port Conflicts
```bash
# Check if port is in use
netstat -tulpn | grep :5000

# Use different port
python start_web.py --port 8080
```

#### Database Issues
```bash
# Reset database
rm hiber_proxy.db
python -m hiber_proxy init-db

# Check database permissions
ls -la hiber_proxy.db
```

### Dependency Issues

#### Missing System Libraries
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install python3-dev build-essential

# CentOS/RHEL
sudo yum install python3-devel gcc

# macOS (with Homebrew)
brew install python3
```

#### SSL/TLS Issues
```bash
# Update certificates
pip install --upgrade certifi

# Install SSL support
pip install pyopenssl cryptography
```

## 📁 Directory Structure

After installation, your directory should look like:
```
HiberNet-Enhanced/
├── HibernetV3.0.py          # Main stress testing tool
├── start_web.py             # Web interface launcher
├── config.json              # Configuration file
├── requirements.txt         # Python dependencies
├── hiber_proxy/             # Proxy management module
│   ├── core/               # Core functionality
│   ├── web/                # Web interface
│   ├── utils/              # Utilities
│   └── scrapers/           # Proxy scrapers
├── hiber_venv/             # Virtual environment
├── logs/                   # Log files
├── docs/                   # Documentation
└── legacy/                 # Legacy tools
```

## ✅ Next Steps

1. **[Quick Start Guide](quick-start.md)** - Basic usage examples
2. **[Configuration Guide](configuration.md)** - Customize your setup
3. **[User Guides](../user-guides/)** - Detailed component documentation
4. **[API Documentation](../api/)** - Programmatic access

## 🆘 Getting Help

- **Documentation**: Check the [user guides](../user-guides/) for detailed instructions
- **Issues**: Report problems on the GitHub repository
- **Configuration**: See the [configuration guide](configuration.md) for setup help
- **API Reference**: Check the [API documentation](../api/) for programmatic usage

## 🙏 Credits

**HiberNet Enhanced** is an enhanced version of the original Hibernet project. We acknowledge and thank the original developers:

- **Original Project**: [Hibernet](https://github.com/All3xJ/Hibernet.git) by All3xJ
- **Enhanced Version**: [HiberNet Enhanced](https://github.com/skizap/HiberNet-Enhanced) by skizap

This enhanced version builds upon the original work with significant improvements including:
- Modern web interface with dark orange terminal aesthetic
- Advanced database integration and management
- Enhanced proxy validation and health monitoring
- Real-time analytics and reporting
- Comprehensive API and documentation

We maintain this enhanced version while respecting and crediting the original project's foundation.

---

**Installation Complete!** 🎉

You're now ready to use HiberNet Enhanced for network testing and proxy management.
