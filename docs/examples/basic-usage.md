# Basic Usage Examples

This guide provides practical examples for common HiberNet Enhanced usage scenarios. Perfect for getting started quickly with real-world applications.

> **Enhanced Examples**: These examples showcase the enhanced capabilities of HiberNet Enhanced compared to the original Hibernet project.

## 🚀 Quick Start Examples

### Example 1: Basic Proxy Management
```bash
# Download some proxies
python -m hiber_proxy download --protocol http

# Check their health
python -m hiber_proxy check --limit 50

# List working proxies
python -m hiber_proxy list --working-only

# Export to file
python -m hiber_proxy export --working-only --output working_proxies.txt
```

### Example 2: Simple Stress Test
```bash
# Basic HTTP stress test
python HibernetV3.0.py \
  --target https://httpbin.org/get \
  --threads 100 \
  --duration 30

# View results in real-time
python HibernetV3.0.py \
  --target https://httpbin.org/get \
  --threads 200 \
  --duration 60 \
  --real-time-dashboard
```

### Example 3: Web Interface Setup
```bash
# Start web interface
python start_web.py

# Access at http://localhost:5000
# Use the dark orange terminal interface to:
# - Download proxies
# - Check proxy health
# - View analytics
# - Configure settings
```

## 🔄 Proxy Management Workflows

### Workflow 1: Daily Proxy Refresh
```bash
#!/bin/bash
# daily_proxy_refresh.sh

echo "Starting daily proxy refresh..."

# Download fresh proxies
echo "Downloading proxies..."
python -m hiber_proxy download --protocol http --protocol socks5

# Check health of new proxies
echo "Checking proxy health..."
python -m hiber_proxy check --limit 200 --timeout 10

# Clean up failed proxies
echo "Cleaning up failed proxies..."
python -m hiber_proxy cleanup --max-failures 3

# Export working proxies
echo "Exporting working proxies..."
python -m hiber_proxy export --working-only --output daily_proxies.txt

# Show final statistics
echo "Final statistics:"
python -m hiber_proxy stats

echo "Daily refresh completed!"
```

### Workflow 2: Proxy Quality Assessment
```bash
#!/bin/bash
# proxy_quality_check.sh

echo "Assessing proxy quality..."

# Check all proxies with detailed analysis
python -m hiber_proxy check --detailed --timeout 15

# Show top performers
echo "Top 10 fastest proxies:"
python -m hiber_proxy top --metric response_time --limit 10

# Show geographic distribution
echo "Geographic distribution:"
python -m hiber_proxy analytics --geographic

# Generate quality report
python -m hiber_proxy report --output quality_report.html

echo "Quality assessment completed!"
```

### Workflow 3: Protocol-Specific Management
```bash
#!/bin/bash
# protocol_management.sh

# Manage HTTP proxies
echo "Managing HTTP proxies..."
python -m hiber_proxy download --protocol http
python -m hiber_proxy check --protocol http --limit 100
python -m hiber_proxy export --protocol http --working-only --output http_proxies.txt

# Manage SOCKS5 proxies
echo "Managing SOCKS5 proxies..."
python -m hiber_proxy download --protocol socks5
python -m hiber_proxy check --protocol socks5 --limit 50
python -m hiber_proxy export --protocol socks5 --working-only --output socks5_proxies.txt

echo "Protocol management completed!"
```

## 🎯 Stress Testing Scenarios

### Scenario 1: Website Load Testing
```bash
# Test your website's capacity
python HibernetV3.0.py \
  --target https://your-website.com \
  --threads 500 \
  --duration 120 \
  --real-time-dashboard \
  --export-json load_test_results.json

# Analyze results
echo "Load test completed. Check load_test_results.json for detailed metrics."
```

### Scenario 2: API Endpoint Testing
```bash
# Test API endpoint with different HTTP methods
python HibernetV3.0.py \
  --target https://api.your-service.com/endpoint \
  --threads 200 \
  --duration 60 \
  --protocol http2 \
  --custom-headers headers.txt \
  --export-csv api_test_results.csv
```

### Scenario 3: Multi-Target Testing
```bash
# Create targets file
cat > targets.txt << EOF
https://your-website.com
https://api.your-service.com/v1/health
https://cdn.your-service.com/status
EOF

# Test multiple targets
python HibernetV3.0.py \
  --target-file targets.txt \
  --threads 300 \
  --duration 90 \
  --real-time-dashboard
```

### Scenario 4: Proxy-Enhanced Testing
```bash
# First, prepare working proxies
python -m hiber_proxy download --protocol http
python -m hiber_proxy check --limit 100
python -m hiber_proxy export --working-only --output test_proxies.txt

# Run stress test with proxies
python HibernetV3.0.py \
  --target https://httpbin.org/ip \
  --proxy-file test_proxies.txt \
  --proxy-rotation health_based \
  --threads 200 \
  --duration 60 \
  --real-time-dashboard
```

## 🌐 Web Interface Examples

### Example 1: Dashboard Monitoring
```bash
# Start web interface
python start_web.py

# Open browser to http://localhost:5000
# The dashboard shows:
# - Real-time proxy statistics
# - System health indicators
# - Recent activity logs
# - Quick action buttons
```

### Example 2: Bulk Proxy Import
```bash
# Prepare proxy file
cat > bulk_proxies.txt << EOF
*************:8080
proxy.example.com:3128
user:<EMAIL>:8080
socks5://127.0.0.1:1080
EOF

# Use web interface:
# 1. Navigate to Proxy Management
# 2. Click "Import Proxies"
# 3. Upload bulk_proxies.txt
# 4. Select validation options
# 5. Click "Import and Validate"
```

### Example 3: Real-time Analytics
```bash
# Start web interface with analytics
python start_web.py

# Navigate to Analytics page to view:
# - Performance metrics charts
# - Geographic distribution map
# - Protocol usage statistics
# - Historical trend analysis
```

## 🐍 Python API Examples

### Example 1: Basic Proxy Operations
```python
from hiber_proxy.main import HiberProxyApp

# Initialize application
app = HiberProxyApp()

# Add some proxies
proxy_ids = []
proxy_ids.append(app.add_proxy("http://127.0.0.1:8080"))
proxy_ids.append(app.add_proxy("socks5://127.0.0.1:1080"))

# Check their health
results = app.check_proxies(proxy_ids=proxy_ids)
print(f"Checked {results['total']} proxies")
print(f"Working: {results['working']}, Failed: {results['failed']}")

# List working proxies
working_proxies = app.list_proxies(working_only=True)
for proxy in working_proxies:
    print(f"{proxy['host']}:{proxy['port']} - {proxy['response_time']}ms")

# Clean up
app.close()
```

### Example 2: Automated Proxy Management
```python
from hiber_proxy.main import HiberProxyApp
import time

def automated_proxy_management():
    app = HiberProxyApp()
    
    try:
        # Download fresh proxies
        print("Downloading proxies...")
        download_results = app.download_proxies(protocol="http")
        print(f"Downloaded {download_results['total_downloaded']} proxies")
        
        # Wait a moment for processing
        time.sleep(2)
        
        # Check proxy health
        print("Checking proxy health...")
        check_results = app.check_proxies(limit=100)
        print(f"Health check: {check_results['working']} working, {check_results['failed']} failed")
        
        # Get statistics
        stats = app.get_statistics()
        print(f"Total proxies: {stats['total_proxies']}")
        print(f"Success rate: {stats['success_rate']:.2%}")
        
        # Export working proxies
        app.export_proxies("auto_proxies.txt", working_only=True)
        print("Exported working proxies to auto_proxies.txt")
        
    finally:
        app.close()

if __name__ == "__main__":
    automated_proxy_management()
```

### Example 3: Custom Validation
```python
from hiber_proxy.core.validation import ProxyValidator
from hiber_proxy.main import HiberProxyApp

def custom_proxy_validation():
    # Initialize with custom settings
    validator = ProxyValidator(
        timeout=15,
        max_retries=3,
        test_url="https://httpbin.org/ip"
    )
    
    # Test specific proxies
    test_proxies = [
        "http://127.0.0.1:8080",
        "socks5://127.0.0.1:1080",
        "http://proxy.example.com:3128"
    ]
    
    print("Testing proxies with custom validation...")
    for proxy in test_proxies:
        result = validator.validate_proxy(proxy)
        if result.is_valid:
            print(f"✓ {proxy} - {result.response_time}ms")
        else:
            print(f"✗ {proxy} - {result.error}")

if __name__ == "__main__":
    custom_proxy_validation()
```

## 📊 Configuration Examples

### Example 1: Basic Configuration
```json
{
  "database": {
    "path": "my_proxies.db",
    "timeout": 30
  },
  "validation": {
    "timeout": 10,
    "max_retries": 3
  },
  "web": {
    "host": "0.0.0.0",
    "port": 8080
  }
}
```

### Example 2: Performance-Optimized Configuration
```json
{
  "database": {
    "path": "hiber_proxy.db",
    "pool_size": 20,
    "timeout": 60
  },
  "validation": {
    "timeout": 15,
    "max_retries": 5,
    "concurrent_checks": 100
  },
  "scraping": {
    "max_concurrent": 20,
    "timeout": 45
  },
  "memory": {
    "max_memory_mb": 2048,
    "cleanup_threshold": 0.8
  }
}
```

### Example 3: Security-Focused Configuration
```json
{
  "web": {
    "host": "127.0.0.1",
    "port": 5000,
    "debug": false,
    "secret_key": "your-secure-secret-key",
    "rate_limiting": {
      "enabled": true,
      "requests_per_minute": 60
    }
  },
  "logging": {
    "level": "INFO",
    "file": "/var/log/hiber_proxy/app.log"
  },
  "database": {
    "backup_enabled": true,
    "backup_interval": 3600
  }
}
```

## 🔧 Integration Examples

### Example 1: Shell Script Integration
```bash
#!/bin/bash
# integrate_with_other_tools.sh

# Get working proxies from HiberProxy
python -m hiber_proxy export --working-only --format json --output temp_proxies.json

# Use with curl for testing
PROXY=$(jq -r '.[0] | "\(.host):\(.port)"' temp_proxies.json)
curl --proxy $PROXY https://httpbin.org/ip

# Use with other tools
echo "Using proxy: $PROXY"
wget --proxy=on --http-proxy=$PROXY https://httpbin.org/get

# Cleanup
rm temp_proxies.json
```

### Example 2: Python Script Integration
```python
import requests
from hiber_proxy.main import HiberProxyApp

def use_proxies_with_requests():
    # Get working proxies
    app = HiberProxyApp()
    proxies = app.list_proxies(working_only=True, limit=10)
    app.close()
    
    # Use with requests library
    for proxy in proxies:
        proxy_url = f"http://{proxy['host']}:{proxy['port']}"
        proxies_dict = {
            'http': proxy_url,
            'https': proxy_url
        }
        
        try:
            response = requests.get(
                'https://httpbin.org/ip',
                proxies=proxies_dict,
                timeout=10
            )
            print(f"Proxy {proxy_url}: {response.json()}")
            break
        except Exception as e:
            print(f"Proxy {proxy_url} failed: {e}")

if __name__ == "__main__":
    use_proxies_with_requests()
```

### Example 3: Monitoring Integration
```bash
#!/bin/bash
# monitoring_integration.sh

# Get proxy statistics
STATS=$(python -m hiber_proxy stats --format json)

# Extract metrics
TOTAL=$(echo $STATS | jq '.total_proxies')
WORKING=$(echo $STATS | jq '.working_proxies')
SUCCESS_RATE=$(echo $STATS | jq '.success_rate')

# Send to monitoring system (example with curl)
curl -X POST https://monitoring.example.com/metrics \
  -H "Content-Type: application/json" \
  -d "{
    \"service\": \"hibernet\",
    \"metrics\": {
      \"total_proxies\": $TOTAL,
      \"working_proxies\": $WORKING,
      \"success_rate\": $SUCCESS_RATE
    }
  }"

echo "Metrics sent to monitoring system"
```

## 🚨 Troubleshooting Examples

### Example 1: Connection Issues
```bash
# Test network connectivity
python -m hiber_proxy download --test-sources

# Check specific proxy
python -m hiber_proxy check --ids 123 --verbose

# Test with different timeout
python -m hiber_proxy check --timeout 30 --retries 5
```

### Example 2: Performance Issues
```bash
# Check system resources
python -m hiber_proxy stats --system

# Optimize database
python -m hiber_proxy optimize

# Reduce concurrent operations
python -m hiber_proxy check --concurrent 10
```

### Example 3: Debug Mode
```bash
# Enable debug logging
python -m hiber_proxy --debug check

# Save debug output
python -m hiber_proxy --debug check 2> debug.log

# Analyze debug output
grep ERROR debug.log
grep WARNING debug.log
```

---

**Basic Usage Mastery!** 🎉

You now have practical examples for all common HiberNet Enhanced usage scenarios and can adapt them to your specific needs.

## 🙏 Credits

These enhanced examples build upon the original [Hibernet project](https://github.com/All3xJ/Hibernet.git) by All3xJ, showcasing the additional capabilities and improvements in HiberNet Enhanced.
